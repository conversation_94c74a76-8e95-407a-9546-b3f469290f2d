# admin平台用户模型响应模式
from datetime import datetime
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, EmailStr
from sqlalchemy.orm import Session

from app.api.deps import (
    check_permissions,
)
from app.api.endpoints.auth import get_password_hash
from app.db.session import get_db
from app.models.user import User, UserRole

router = APIRouter()


class UserBase(BaseModel):
    username: str
    email: EmailStr | None = None
    nickname: str | None = None
    is_active: bool | None = True
    is_superuser: bool = False
    role_id: int | None = None


class UserCreate(UserBase):
    password: str


class UserUpdate(BaseModel):
    email: EmailStr | None = None
    nickname: str | None = None
    password: str | None = None
    is_active: bool | None = None
    role_id: int | None = None


class UserInDBBase(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class UserInDB(UserInDBBase):
    password: str


class UserResponse(UserInDBBase):
    role: str | None = None
    permissions: list[str] = []


@router.get("/", response_model=list[UserResponse])
async def get_users(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(check_permissions(["users:list"])),
) -> Any:
    """获取用户列表"""
    users = db.query(User).offset(skip).limit(limit).all()

    # 构建响应
    result = []
    for user in users:
        user_data = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "nickname": user.nickname,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "role_id": user.role_id,
            "created_at": user.created_at,
            "updated_at": user.updated_at,
            "role": user.role.name if user.role else None,
            "permissions": user.permissions,
        }
        result.append(user_data)

    return result


@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    *,
    db: Session = Depends(get_db),
    user_in: UserCreate,
    current_user: User = Depends(check_permissions(["users:create"])),
) -> Any:
    """创建新用户"""
    # 检查用户名是否已存在
    user = db.query(User).filter(User.username == user_in.username).first()
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在",
        )

    # 检查邮箱是否已存在
    if user_in.email:
        user = db.query(User).filter(User.email == user_in.email).first()
        if user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在",
            )

    # 检查角色是否存在
    if user_in.role_id:
        role = db.query(UserRole).filter(UserRole.id == user_in.role_id).first()
        if not role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色不存在",
            )
    else:
        # 使用默认角色
        role = db.query(UserRole).filter(UserRole.is_default).first()
        if role:
            user_in.role_id = role.id

    # 创建用户
    user = User(
        username=user_in.username,
        email=user_in.email,
        nickname=user_in.nickname or user_in.username,
        password=get_password_hash(user_in.password),
        is_active=user_in.is_active,
        is_superuser=user_in.is_superuser,
        role_id=user_in.role_id,
    )
    db.add(user)
    db.commit()
    db.refresh(user)

    # 构建响应
    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "nickname": user.nickname,
        "is_active": user.is_active,
        "is_superuser": user.is_superuser,
        "role_id": user.role_id,
        "created_at": user.created_at,
        "updated_at": user.updated_at,
        "role": user.role.name if user.role else None,
        "permissions": user.permissions,
    }


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    *,
    db: Session = Depends(get_db),
    user_id: int,
    current_user: User = Depends(check_permissions(["users:read"])),
) -> Any:
    """获取特定用户的详细信息"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 构建响应
    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "nickname": user.nickname,
        "is_active": user.is_active,
        "is_superuser": user.is_superuser,
        "role_id": user.role_id,
        "created_at": user.created_at,
        "updated_at": user.updated_at,
        "role": user.role.name if user.role else None,
        "permissions": user.permissions,
    }


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    *,
    db: Session = Depends(get_db),
    user_id: int,
    user_in: UserUpdate,
    current_user: User = Depends(check_permissions(["users:update"])),
) -> Any:
    """更新用户信息"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 检查是否有权限更新超级管理员
    if user.is_superuser and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权更新超级管理员",
        )

    # 检查邮箱是否已存在
    if user_in.email and user_in.email != user.email:
        existing_user = db.query(User).filter(User.email == user_in.email).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在",
            )

    # 检查角色是否存在
    if user_in.role_id and user_in.role_id != user.role_id:
        role = db.query(UserRole).filter(UserRole.id == user_in.role_id).first()
        if not role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色不存在",
            )

    # 更新用户信息
    if user_in.email is not None:
        user.email = user_in.email
    if user_in.nickname is not None:
        user.nickname = user_in.nickname
    if user_in.password is not None:
        user.password = get_password_hash(user_in.password)
    if user_in.is_active is not None:
        user.is_active = user_in.is_active
    if user_in.role_id is not None:
        user.role_id = user_in.role_id

    db.commit()
    db.refresh(user)

    # 构建响应
    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "nickname": user.nickname,
        "is_active": user.is_active,
        "is_superuser": user.is_superuser,
        "role_id": user.role_id,
        "created_at": user.created_at,
        "updated_at": user.updated_at,
        "role": user.role.name if user.role else None,
        "permissions": user.permissions,
    }


@router.delete("/{user_id}", response_model=UserResponse)
async def delete_user(
    *,
    db: Session = Depends(get_db),
    user_id: int,
    current_user: User = Depends(check_permissions(["users:delete"])),
) -> Any:
    """删除用户"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 检查是否有权限删除超级管理员
    if user.is_superuser and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权删除超级管理员",
        )

    # 不允许删除自己
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己的账户",
        )

    # 保存用户信息用于返回
    user_data = {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "nickname": user.nickname,
        "is_active": user.is_active,
        "is_superuser": user.is_superuser,
        "role_id": user.role_id,
        "created_at": user.created_at,
        "updated_at": user.updated_at,
        "role": user.role.name if user.role else None,
        "permissions": user.permissions,
    }

    # 删除用户
    db.delete(user)
    db.commit()

    return user_data
