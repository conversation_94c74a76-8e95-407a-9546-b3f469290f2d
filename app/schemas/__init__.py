# 使schemas目录成为一个Python包
from app.schemas.article import (
    Article,
    ArticleCreate,
    ArticleList,
    ArticleListWithStats,
    ArticleUpdate,
    ArticleWithStats,
)
from app.schemas.comment import Comment, CommentCreate, CommentList, CommentUpdate
from app.schemas.favorite import (
    ContentFavoriteInfo,
    Favorite,
    FavoriteCreate,
    FavoriteHistory,
    FavoriteStats,
    FavoriteStatus,
    FavoriteToggle,
    FavoriteUpdate,
    FavoriteWithContent,
)
from app.schemas.game import Game, GameCreate, GameList, GameUpdate
from app.schemas.like import (
    ContentLikeInfo,
    Like,
    LikeCreate,
    LikeHistory,
    LikeStats,
    LikeStatus,
    LikeToggle,
)
from app.schemas.review import Review, ReviewCreate, ReviewList, ReviewUpdate
from app.schemas.video import (
    Video,
    VideoCreate,
    VideoList,
    VideoListWithStats,
    VideoUpdate,
    VideoWithStats,
)

# 在这里导入所有模式，以便在其他地方可以通过app.schemas导入
